'use client';

import { useEffect, useState } from 'react';
import type { Mystery } from '@/types/mystery';
import { getDailyMystery, MysteryNotFoundError } from '@/lib/mysteryService';
import { DailyMysteryCard } from '@/components/mystery/DailyMysteryCard';
import { SuspectList } from '@/components/mystery/SuspectList';
import { CrimeExplanation } from '@/components/mystery/CrimeExplanation';
import { NoMysteryAvailable } from '@/components/mystery/NoMysteryAvailable';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Drama } from 'lucide-react'; // Drama for title icon

export default function HomePage() {
  const [currentMystery, setCurrentMystery] = useState<Mystery | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isConnectionError, setIsConnectionError] = useState(false);

  const [draftSelectedSuspectId, setDraftSelectedSuspectId] = useState<string | null>(null);
  const [finalAccusedSuspectId, setFinalAccusedSuspectId] = useState<string | null>(null);
  const [isCorrectGuess, setIsCorrectGuess] = useState<boolean | null>(null);
  const [showExplanation, setShowExplanation] = useState(false);

  useEffect(() => {
    async function fetchMystery() {
      setIsLoading(true);
      setError(null);
      setIsConnectionError(false);

      try {
        const mystery = await getDailyMystery();
        setCurrentMystery(mystery);
      } catch (error) {
        console.error("Failed to fetch mystery:", error);

        if (error instanceof MysteryNotFoundError) {
          setError(error.message);
          setIsConnectionError(false);
        } else {
          setError(error instanceof Error ? error.message : 'An unexpected error occurred');
          setIsConnectionError(true);
        }
      } finally {
        setIsLoading(false);
      }
    }
    fetchMystery();
  }, []);

  const handleSelectSuspect = (suspectId: string) => {
    setDraftSelectedSuspectId(suspectId);
  };

  const handleConfirmAccusation = () => {
    if (!draftSelectedSuspectId || !currentMystery) return;

    setFinalAccusedSuspectId(draftSelectedSuspectId);
    const correct = draftSelectedSuspectId === currentMystery.killerId;
    setIsCorrectGuess(correct);
    setShowExplanation(true); // Reveal explanation after accusation
    // Optional: scroll to explanation or result
    // document.getElementById('explanation-section')?.scrollIntoView({ behavior: 'smooth' });
  };

  const resetDailyState = () => {
    // This would be more complex with actual daily logic, e.g., tied to date change
    // For now, simple reset for demo purposes if we want a "play again" feel for same mystery
    setDraftSelectedSuspectId(null);
    setFinalAccusedSuspectId(null);
    setIsCorrectGuess(null);
    setShowExplanation(false);
    // Potentially re-fetch if new day logic is implemented in service
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background text-foreground flex flex-col items-center justify-center p-4 md:p-8 font-body">
        <Loader2 className="h-16 w-16 text-primary animate-spin" />
        <p className="mt-4 text-xl font-headline text-primary">Unearthing today's mystery...</p>
      </div>
    );
  }

  if (error || !currentMystery) {
    return (
      <div className="min-h-screen bg-background text-foreground p-4 md:p-8 flex flex-col items-center font-body selection:bg-accent selection:text-accent-foreground">
        <header className="w-full max-w-4xl mb-8 md:mb-12 text-center">
          <div className="flex items-center justify-center gap-3 mb-2">
            <Drama className="h-12 w-12 text-primary" />
            <h1 className="text-4xl md:text-6xl font-headline text-primary">
              DailyDeduction
            </h1>
          </div>
          <p className="text-md md:text-lg text-muted-foreground italic">
            A new enigma unfolds each day. Can you piece together the clues?
          </p>
        </header>

        <main className="w-full max-w-4xl">
          <NoMysteryAvailable
            error={error || undefined}
            isConnectionError={isConnectionError}
          />
        </main>

        <footer className="w-full max-w-4xl mt-12 md:mt-16 pt-8 border-t border-primary/20 text-center">
          <p className="text-sm text-muted-foreground">
            &copy; {new Date().getFullYear()} DailyDeduction. The truth is out there.
          </p>
        </footer>
      </div>
    );
  }
  
  const accusedSuspect = currentMystery.suspects.find(s => s.id === finalAccusedSuspectId);

  return (
    <div className="min-h-screen bg-background text-foreground p-4 md:p-8 flex flex-col items-center font-body selection:bg-accent selection:text-accent-foreground">
      <header className="w-full max-w-4xl mb-8 md:mb-12 text-center">
        <div className="flex items-center justify-center gap-3 mb-2">
          <Drama className="h-12 w-12 text-primary" />
          <h1 className="text-4xl md:text-6xl font-headline text-primary">
            DailyDeduction
          </h1>
        </div>
        <p className="text-md md:text-lg text-muted-foreground italic">
          A new enigma unfolds each day. Can you piece together the clues?
        </p>
      </header>

      <main className="w-full max-w-4xl space-y-8 md:space-y-12">
        <DailyMysteryCard mystery={currentMystery} />
        
        {!finalAccusedSuspectId && (
          <SuspectList
            suspects={currentMystery.suspects}
            onSelectSuspect={handleSelectSuspect}
            onConfirmAccusation={handleConfirmAccusation}
            selectedSuspectId={draftSelectedSuspectId}
            isAccusationMade={!!finalAccusedSuspectId}
          />
        )}
        
        {showExplanation && (
          <div id="explanation-section">
            <CrimeExplanation
              explanation={currentMystery.explanation}
              killerName={currentMystery.suspects.find(s => s.id === currentMystery.killerId)?.name || 'The Culprit'}
              isCorrectGuess={isCorrectGuess}
              accusedName={accusedSuspect?.name}
            />
          </div>
        )}
      </main>

      <footer className="w-full max-w-4xl mt-12 md:mt-16 pt-8 border-t border-primary/20 text-center">
        <p className="text-sm text-muted-foreground">
          &copy; {new Date().getFullYear()} DailyDeduction. The truth is out there.
        </p>
      </footer>
    </div>
  );
}
