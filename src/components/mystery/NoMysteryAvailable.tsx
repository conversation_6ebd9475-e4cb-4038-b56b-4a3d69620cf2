import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { File<PERSON>, <PERSON>, Skull } from 'lucide-react';

interface NoMysteryAvailableProps {
  error?: string;
  isConnectionError?: boolean;
}

export function NoMysteryAvailable({ error, isConnectionError = false }: NoMysteryAvailableProps) {
  const getIcon = () => {
    if (isConnectionError) return <Clock className="h-16 w-16 text-primary/60" />;
    return <FileX className="h-16 w-16 text-primary/60" />;
  };

  const getTitle = () => {
    if (isConnectionError) return "The Investigation is Delayed";
    return "No Murders Today... Yet";
  };

  const getMessage = () => {
    if (isConnectionError) {
      return "Our detectives are having trouble accessing the case files. Please check your connection and try again.";
    }
    return "The case files are empty today. It seems the criminal underworld is taking a well-deserved rest.";
  };

  const getSubMessage = () => {
    if (isConnectionError) {
      return "The mystery archives should be accessible shortly.";
    }
    return "Return tomorrow when fresh mysteries await your keen detective skills.";
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Card className="shadow-xl border-primary/30 bg-card/80 backdrop-blur-sm text-center">
        <CardHeader className="border-b border-primary/20 pb-8">
          <div className="flex flex-col items-center space-y-4">
            {getIcon()}
            <CardTitle className="font-headline text-3xl md:text-4xl text-primary">
              {getTitle()}
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className="pt-8 pb-12">
          <div className="space-y-6">
            <p className="text-lg leading-relaxed text-foreground/90 font-body max-w-2xl mx-auto">
              {getMessage()}
            </p>
            
            <div className="flex items-center justify-center space-x-2 text-muted-foreground">
              <Skull className="h-5 w-5" />
              <span className="text-sm italic font-body">
                {getSubMessage()}
              </span>
              <Skull className="h-5 w-5" />
            </div>

            {error && (
              <div className="mt-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
                <p className="text-sm text-destructive font-body">
                  <strong>Technical Details:</strong> {error}
                </p>
              </div>
            )}

            <div className="mt-8 space-y-2">
              <p className="text-sm text-muted-foreground font-body">
                "In the absence of crime, the detective's mind grows sharper."
              </p>
              <p className="text-xs text-muted-foreground/70 font-body italic">
                — Anonymous Detective, 1892
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
