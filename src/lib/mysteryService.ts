import type { Mystery, FirestoreMystery, LegacySuspect, LegacyCrimeExplanation } from '@/types/mystery';
import { db } from './firebase';
import { doc, getDoc } from 'firebase/firestore';

// Helper function to transform Firestore data to legacy format
const transformFirestoreMystery = (firestoreData: FirestoreMystery, docId: string): Mystery => {
  // Transform suspects to legacy format with generated IDs
  const legacySuspects: LegacySuspect[] = firestoreData.Suspects.map((suspect, index) => ({
    id: `suspect_${index + 1}`,
    name: suspect.name,
    description: `${suspect.motive} ${suspect.means} ${suspect.opportunity}`.trim(),
    imageHint: 'mysterious person' // Default hint for AI-generated images
  }));

  // Find the killer ID by matching the killer name from Resolution
  const killerName = firestoreData.Resolution.killer;
  const killerId = legacySuspects.find(s => s.name === killerName)?.id || 'suspect_1';

  // Transform explanation to legacy format
  const legacyExplanation: LegacyCrimeExplanation = {
    motive: firestoreData.Resolution.motive,
    method: firestoreData.Resolution.method,
    keyEvidence: firestoreData.Resolution.key_evidence
  };

  return {
    id: docId,
    title: firestoreData.Title,
    story: firestoreData.FullText,
    suspects: legacySuspects,
    killerId,
    explanation: legacyExplanation
  };
};

// Helper function to get today's date in yyyy-MM-dd format
const getTodayDateId = (): string => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Custom error class for mystery not found
export class MysteryNotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'MysteryNotFoundError';
  }
}

export const getDailyMystery = async (): Promise<Mystery> => {
  try {
    // Get today's date in the expected format
    const todayId = getTodayDateId();

    // Reference to the mystery document
    const mysteryRef = doc(db, 'mysteries', todayId);

    // Fetch the document
    const mysterySnap = await getDoc(mysteryRef);

    if (mysterySnap.exists()) {
      const firestoreData = mysterySnap.data() as FirestoreMystery;
      return transformFirestoreMystery(firestoreData, todayId);
    } else {
      throw new MysteryNotFoundError(`No mystery available for ${todayId}`);
    }
  } catch (error) {
    if (error instanceof MysteryNotFoundError) {
      throw error; // Re-throw mystery not found errors
    }
    // For other errors (connection issues, etc.), throw a generic error
    throw new Error('Unable to retrieve today\'s mystery. Please check your connection and try again.');
  }
};